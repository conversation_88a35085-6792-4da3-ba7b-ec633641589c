steps:
  # Build the container image with optimizations
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/truthlens-backend:latest'
      - '--cache-from'
      - 'gcr.io/$PROJECT_ID/truthlens-backend:latest'
      - '.'
    env:
      - 'DOCKER_BUILDKIT=1'

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/truthlens-backend:latest']

  # Deploy container image to Cloud Run with free tier optimizations
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
    - 'run'
    - 'deploy'
    - 'truthlens-backend'
    - '--image'
    - 'gcr.io/$PROJECT_ID/truthlens-backend:latest'
    - '--region'
    - 'us-central1'  # Changed to us-central1 for better free tier coverage
    - '--platform'
    - 'managed'
    - '--allow-unauthenticated'
    - '--memory'
    - '1Gi'  # Reduced memory for free tier optimization
    - '--cpu'
    - '1'    # Reduced CPU for free tier optimization
    - '--max-instances'
    - '5'    # Reduced max instances for cost control
    - '--min-instances'
    - '0'    # Scale to zero for cost savings
    - '--timeout'
    - '300'
    - '--concurrency'
    - '80'   # Optimize concurrency for better resource usage
    - '--port'
    - '8080'
    - '--set-env-vars'
    - 'PORT=8080,FLASK_ENV=production'

# Use timeout for build optimization
timeout: '1200s'  # 20 minutes timeout

# Machine type optimization for free tier
options:
  machineType: 'E2_HIGHCPU_8'  # Cost-effective machine type
  diskSizeGb: '100'
  logging: 'CLOUD_LOGGING_ONLY'

images:
  - 'gcr.io/$PROJECT_ID/truthlens-backend:latest'
