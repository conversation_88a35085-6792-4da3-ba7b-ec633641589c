steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/truthlens-backend:latest', '.']

  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/truthlens-backend:latest']

  # Deploy container image to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
    - 'run'
    - 'deploy'
    - 'truthlens-backend'
    - '--image'
    - 'gcr.io/$PROJECT_ID/truthlens-backend:latest'
    - '--region'
    - 'us-east1'
    - '--platform'
    - 'managed'
    - '--allow-unauthenticated'
    - '--memory'
    - '2Gi'
    - '--cpu'
    - '2'
    - '--max-instances'
    - '10'
    - '--timeout'
    - '300'

images:
  - 'gcr.io/$PROJECT_ID/truthlens-backend:latest'
