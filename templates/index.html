<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TruthLens AI - Fake News Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'typewriter': 'typewriter 4s steps(40) 1s 1 normal both',
                        'blink': 'blink 1s infinite',
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'pulse-glow': 'pulseGlow 2s ease-in-out infinite alternate',
                        'float': 'float 6s ease-in-out infinite',
                        'matrix': 'matrix 20s linear infinite',
                        'neon-pulse': 'neonPulse 3s ease-in-out infinite alternate',
                        'cyber-glow': 'cyberGlow 4s ease-in-out infinite',
                        'data-stream': 'dataStream 15s linear infinite',
                    },
                    keyframes: {
                        typewriter: {
                            '0%': { width: '0ch' },
                            '100%': { width: '20ch' }
                        },
                        blink: {
                            '0%, 50%': { borderColor: 'transparent' },
                            '51%, 100%': { borderColor: 'currentColor' }
                        },
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(40px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        pulseGlow: {
                            '0%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 40px rgba(59, 130, 246, 0.8)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                            '50%': { transform: 'translateY(-20px) rotate(180deg)' }
                        },
                        matrix: {
                            '0%': { transform: 'translateY(-100vh)' },
                            '100%': { transform: 'translateY(100vh)' }
                        },
                        neonPulse: {
                            '0%': {
                                boxShadow: '0 0 20px #ff6b35, 0 0 40px #ff6b35, 0 0 60px #ff6b35',
                                textShadow: '0 0 10px #ff6b35'
                            },
                            '100%': {
                                boxShadow: '0 0 40px #ff6b35, 0 0 80px #ff6b35, 0 0 120px #ff6b35',
                                textShadow: '0 0 20px #ff6b35'
                            }
                        },
                        cyberGlow: {
                            '0%, 100%': {
                                boxShadow: '0 0 30px rgba(0, 255, 255, 0.6), inset 0 0 30px rgba(0, 255, 255, 0.1)',
                                borderColor: 'rgba(0, 255, 255, 0.6)'
                            },
                            '50%': {
                                boxShadow: '0 0 60px rgba(255, 107, 53, 0.8), inset 0 0 60px rgba(255, 107, 53, 0.2)',
                                borderColor: 'rgba(255, 107, 53, 0.8)'
                            }
                        },
                        dataStream: {
                            '0%': { transform: 'translateX(-100vw) rotate(0deg)', opacity: '0' },
                            '10%': { opacity: '1' },
                            '90%': { opacity: '1' },
                            '100%': { transform: 'translateX(100vw) rotate(360deg)', opacity: '0' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .glass {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 107, 53, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 107, 53, 0.2);
        }
        .typewriter {
            overflow: hidden;
            border-right: 3px solid #ff6b35;
            white-space: nowrap;
            margin: 0 auto;
            animation: typewriter 4s steps(40) 1s 1 normal both, blink 1s infinite;
            text-shadow: 0 0 10px rgba(255, 107, 53, 0.8);
        }
        .cyber-border {
            border: 2px solid transparent;
            background: linear-gradient(45deg, #ff6b35, #00ffff, #ff6b35) border-box;
            border-radius: 12px;
        }
        .matrix-text {
            font-family: 'Courier New', monospace;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white font-sans relative">
    <!-- Simplified Background Elements -->
    <div class="fixed inset-0 pointer-events-none z-0">
        <!-- Neon Grid -->
        <div class="absolute inset-0 opacity-10" style="background-image: linear-gradient(rgba(255,107,53,0.2) 1px, transparent 1px), linear-gradient(90deg, rgba(255,107,53,0.2) 1px, transparent 1px); background-size: 40px 40px;"></div>

        <!-- Floating Orbs -->
        <div class="absolute -top-20 -right-20 w-40 h-40 md:w-80 md:h-80 bg-gradient-to-r from-orange-500/20 to-cyan-500/20 rounded-full blur-3xl animate-float"></div>
        <div class="absolute -bottom-20 -left-20 w-40 h-40 md:w-80 md:h-80 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-full blur-3xl animate-float" style="animation-delay: 2s;"></div>

        <!-- Cyber Accents -->
        <div class="hidden md:block absolute top-20 right-20 w-12 h-12 border-2 border-cyan-400/30 transform rotate-45 animate-cyber-glow"></div>
        <div class="hidden md:block absolute bottom-20 left-20 w-8 h-8 border-2 border-orange-400/30 transform rotate-12 animate-cyber-glow" style="animation-delay: 2s;"></div>
    </div>

    <div class="relative z-10 container mx-auto px-4 py-6 md:py-8 pb-12 md:pb-16">
        <!-- Header -->
        <div class="text-center mb-8 md:mb-12 animate-fade-in">
            <div class="flex flex-col md:flex-row items-center justify-center gap-4 md:gap-6 mb-6 md:mb-8">
                <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-r from-orange-500 to-cyan-500 rounded-full flex items-center justify-center text-3xl md:text-4xl animate-neon-pulse border-2 border-orange-400/50">
                    🔍
                </div>
                <h1 class="text-4xl md:text-7xl font-bold bg-gradient-to-r from-orange-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent animate-neon-pulse">
                    TruthLens AI
                </h1>
            </div>
            <p class="text-xl md:text-3xl typewriter mb-4 md:mb-6 font-bold text-orange-300">Is that headline true?</p>
            <p class="text-lg md:text-xl font-medium max-w-3xl mx-auto text-cyan-200 leading-relaxed px-4">
                🤖 ML-powered fake news detection with Kaggle-trained models + Gemini AI explanations.
                <br class="hidden md:block">📷 OCR text extraction, 🌍 23 languages, 🔊 voice output support.
            </p>

            <!-- Language Selection -->
            <div class="mt-6 flex flex-col md:flex-row items-center justify-center gap-2 md:gap-4 px-4">
                <label class="text-white font-semibold text-base md:text-lg">🌍 Output Language:</label>
                <select id="languageSelect" class="w-full md:w-auto px-4 md:px-6 py-2 md:py-3 bg-black/30 border-2 border-orange-400/60 rounded-xl text-white backdrop-blur-sm focus:outline-none focus:ring-4 focus:ring-orange-500/50 focus:border-orange-400 transition-all duration-300 font-medium text-base md:text-lg">
                    <optgroup label="🇮🇳 Indian Languages">
                        <option value="en">🇺🇸 English</option>
                        <option value="hi">🇮🇳 हिंदी (Hindi)</option>
                        <option value="ta">🇮🇳 தமிழ் (Tamil)</option>
                        <option value="te">🇮🇳 తెలుగు (Telugu)</option>
                        <option value="ml">🇮🇳 മലയാളം (Malayalam)</option>
                        <option value="kn">🇮🇳 ಕನ್ನಡ (Kannada)</option>
                        <option value="bn">🇮🇳 বাংলা (Bengali)</option>
                        <option value="gu">🇮🇳 ગુજરાતી (Gujarati)</option>
                        <option value="mr">🇮🇳 मराठी (Marathi)</option>
                        <option value="pa">🇮🇳 ਪੰਜਾਬੀ (Punjabi)</option>
                    </optgroup>
                    <optgroup label="🌍 International Languages">
                        <option value="es">🇪🇸 Español (Spanish)</option>
                        <option value="fr">🇫🇷 Français (French)</option>
                        <option value="de">🇩🇪 Deutsch (German)</option>
                        <option value="it">🇮🇹 Italiano (Italian)</option>
                        <option value="pt">🇵🇹 Português (Portuguese)</option>
                        <option value="ru">🇷🇺 Русский (Russian)</option>
                        <option value="ja">🇯🇵 日本語 (Japanese)</option>
                        <option value="ko">🇰🇷 한국어 (Korean)</option>
                        <option value="zh">🇨🇳 中文 (Chinese)</option>
                        <option value="ar">🇸🇦 العربية (Arabic)</option>
                        <option value="tr">🇹🇷 Türkçe (Turkish)</option>
                        <option value="nl">🇳🇱 Nederlands (Dutch)</option>
                        <option value="sv">🇸🇪 Svenska (Swedish)</option>
                    </optgroup>
                </select>
            </div>
        </div>

        <!-- Main Input Section -->
        <div class="max-w-4xl mx-auto mb-8 px-4">
            <div class="glass rounded-2xl md:rounded-3xl p-4 md:p-8 shadow-2xl animate-slide-up">
                <form id="analysisForm" class="space-y-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Text Input -->
                        <div>
                            <label class="block text-orange-300 text-lg md:text-xl font-bold mb-3 md:mb-4 flex flex-col md:flex-row md:items-center gap-1 md:gap-2">
                                📝 Enter News Text
                                <span class="text-cyan-400 text-sm font-normal">(Any Language)</span>
                            </label>
                            <div class="relative">
                                <textarea
                                    id="textInput"
                                    placeholder="Paste your news article, headline, or any text content here in any language..."
                                    class="w-full h-36 md:h-44 px-4 md:px-6 py-3 md:py-4 pr-16 bg-black/60 border-2 border-orange-400/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-orange-500/50 focus:border-orange-400 resize-none backdrop-blur-sm transition-all duration-300 font-medium text-base md:text-lg animate-cyber-glow"
                                ></textarea>
                                <!-- Voice Input Button -->
                                <button
                                    type="button"
                                    id="voiceInputBtn"
                                    class="absolute top-3 right-3 w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-red-400/50"
                                    title="Click to start voice input"
                                >
                                    <span id="micIcon" class="text-white text-lg">🎤</span>
                                </button>
                                <!-- Voice Status Indicator -->
                                <div id="voiceStatus" class="hidden absolute top-14 right-3 bg-black/80 text-white text-xs px-2 py-1 rounded-lg border border-red-400/50">
                                    <span id="voiceStatusText">Listening...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Image Upload -->
                        <div>
                            <label class="block text-cyan-300 text-lg md:text-xl font-bold mb-3 md:mb-4 flex flex-col md:flex-row md:items-center gap-1 md:gap-2">
                                🖼️ Upload News Image
                                <span class="text-orange-400 text-sm font-normal">(Screenshots/Photos)</span>
                            </label>
                            <div
                                id="dropZone"
                                class="h-36 md:h-44 border-2 border-dashed border-cyan-400/50 rounded-xl bg-black/40 flex flex-col items-center justify-center cursor-pointer transition-all duration-300 hover:border-cyan-400/80 hover:bg-black/60 animate-cyber-glow backdrop-blur-sm"
                            >
                                <input type="file" id="imageInput" accept="image/*" class="hidden">
                                <div id="dropContent" class="text-center">
                                    <div class="text-4xl mb-2">📁</div>
                                    <p class="text-white/80">Drop image here or click to browse</p>
                                    <p class="text-white/60 text-sm mt-1">JPG, PNG, WebP up to 10MB</p>
                                </div>
                                <div id="filePreview" class="hidden text-center">
                                    <div class="text-4xl mb-2">✅</div>
                                    <p id="fileName" class="text-white/90"></p>
                                    <button type="button" id="removeFile" class="mt-2 px-3 py-1 bg-red-500/20 border border-red-500/40 rounded-lg text-sm hover:bg-red-500/30 transition-colors">
                                        Remove
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analyze Button -->
                    <div class="text-center">
                        <button
                            type="submit"
                            id="analyzeBtn"
                            class="w-full md:w-auto px-8 md:px-12 py-4 md:py-5 bg-gradient-to-r from-orange-500 via-purple-600 to-cyan-500 hover:from-orange-600 hover:via-purple-700 hover:to-cyan-600 rounded-full text-lg md:text-2xl font-bold shadow-2xl hover:shadow-orange-500/50 transform hover:scale-105 md:hover:scale-110 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none animate-neon-pulse border-2 border-orange-400/50"
                        >
                            <span id="btnText" class="flex items-center justify-center gap-2 md:gap-3">
                                🔍 Analyze for Truth
                                <span class="text-cyan-300">✨</span>
                            </span>
                            <div id="btnLoader" class="hidden flex items-center justify-center gap-2 md:gap-3">
                                <div class="w-5 h-5 md:w-6 md:h-6 border-2 md:border-3 border-white/30 border-t-white rounded-full animate-spin"></div>
                                <span class="text-cyan-300">Analyzing with AI...</span>
                            </div>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden max-w-4xl mx-auto px-4">
            <div class="glass rounded-2xl md:rounded-3xl p-4 md:p-8 shadow-2xl animate-slide-up">
                <h2 class="text-2xl md:text-3xl font-bold text-center mb-6 md:mb-8 flex items-center justify-center gap-2">
                    🔍 Analysis Results
                </h2>

                <div id="resultsContent">
                    <!-- Results will be populated here -->
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col md:flex-row gap-3 md:gap-4 justify-center mt-6 md:mt-8">
                    <button
                        id="analyzeAnother"
                        class="w-full md:w-auto px-6 py-3 bg-white/20 hover:bg-white/30 rounded-full font-medium transition-all duration-300"
                    >
                        🔄 Analyze Another
                    </button>
                    <button
                        id="copyResults"
                        class="w-full md:w-auto px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 rounded-full font-medium transition-all duration-300"
                    >
                        📋 Copy Results
                    </button>
                    <button
                        id="speakResults"
                        class="w-full md:w-auto px-6 py-3 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 rounded-full font-medium transition-all duration-300"
                    >
                        🔊 Speak Results
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let currentResult = null;

        // Define API Base URL globally
        const API_BASE_URL = window.location.hostname === 'localhost'
            ? 'http://localhost:5001'
            : 'https://truthlens-backend-YOUR_PROJECT_ID-uc.a.run.app';

        // DOM Elements
        const form = document.getElementById('analysisForm');
        const textInput = document.getElementById('textInput');
        const imageInput = document.getElementById('imageInput');
        const dropZone = document.getElementById('dropZone');
        const dropContent = document.getElementById('dropContent');
        const filePreview = document.getElementById('filePreview');
        const fileName = document.getElementById('fileName');
        const removeFileBtn = document.getElementById('removeFile');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const btnText = document.getElementById('btnText');
        const btnLoader = document.getElementById('btnLoader');
        const resultsSection = document.getElementById('resultsSection');
        const resultsContent = document.getElementById('resultsContent');
        const analyzeAnotherBtn = document.getElementById('analyzeAnother');
        const copyResultsBtn = document.getElementById('copyResults');
        const speakResultsBtn = document.getElementById('speakResults');
        const voiceInputBtn = document.getElementById('voiceInputBtn');
        const micIcon = document.getElementById('micIcon');
        const voiceStatus = document.getElementById('voiceStatus');
        const voiceStatusText = document.getElementById('voiceStatusText');

        // Voice Recognition Setup
        let recognition = null;
        let isListening = false;

        // Check if browser supports speech recognition
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();

            recognition.continuous = false;
            recognition.interimResults = true;
            recognition.lang = 'en-US'; // Default language

            recognition.onstart = function() {
                isListening = true;
                micIcon.textContent = '🔴';
                voiceInputBtn.classList.add('animate-pulse');
                voiceStatus.classList.remove('hidden');
                voiceStatusText.textContent = 'Listening...';
            };

            recognition.onresult = function(event) {
                let finalTranscript = '';
                let interimTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript;
                    } else {
                        interimTranscript += transcript;
                    }
                }

                if (finalTranscript) {
                    textInput.value = finalTranscript;
                    voiceStatusText.textContent = 'Speech captured!';
                    setTimeout(() => {
                        voiceStatus.classList.add('hidden');
                    }, 2000);
                } else if (interimTranscript) {
                    voiceStatusText.textContent = `Hearing: "${interimTranscript}"`;
                }
            };

            recognition.onerror = function(event) {
                console.error('Speech recognition error:', event.error);
                isListening = false;
                micIcon.textContent = '🎤';
                voiceInputBtn.classList.remove('animate-pulse');
                voiceStatus.classList.add('hidden');

                let errorMessage = 'Voice input failed';
                switch(event.error) {
                    case 'no-speech':
                        errorMessage = 'No speech detected. Please try again.';
                        break;
                    case 'audio-capture':
                        errorMessage = 'Microphone not accessible. Please check permissions.';
                        break;
                    case 'not-allowed':
                        errorMessage = 'Microphone permission denied. Please allow microphone access.';
                        break;
                    case 'network':
                        errorMessage = 'Network error. Please check your connection.';
                        break;
                }
                alert(errorMessage);
            };

            recognition.onend = function() {
                isListening = false;
                micIcon.textContent = '🎤';
                voiceInputBtn.classList.remove('animate-pulse');
                setTimeout(() => {
                    voiceStatus.classList.add('hidden');
                }, 1000);
            };
        } else {
            // Hide voice button if not supported
            voiceInputBtn.style.display = 'none';
        }

        // Voice Input Button Click Handler
        voiceInputBtn.addEventListener('click', function() {
            if (!recognition) {
                alert('Voice input is not supported in your browser. Please use Chrome, Edge, or Safari.');
                return;
            }

            if (isListening) {
                recognition.stop();
                return;
            }

            // Update language based on selected output language
            const languageSelect = document.getElementById('language');
            const selectedLang = languageSelect.value;

            // Map language codes to speech recognition languages
            const langMap = {
                'en': 'en-US',
                'hi': 'hi-IN',
                'es': 'es-ES',
                'fr': 'fr-FR',
                'de': 'de-DE',
                'it': 'it-IT',
                'pt': 'pt-PT',
                'ru': 'ru-RU',
                'ja': 'ja-JP',
                'ko': 'ko-KR',
                'zh': 'zh-CN',
                'ar': 'ar-SA',
                'tr': 'tr-TR',
                'nl': 'nl-NL',
                'sv': 'sv-SE'
            };

            recognition.lang = langMap[selectedLang] || 'en-US';

            try {
                recognition.start();
            } catch (error) {
                console.error('Failed to start speech recognition:', error);
                alert('Failed to start voice input. Please try again.');
            }
        });
        const languageSelect = document.getElementById('languageSelect');

        // File upload handling
        dropZone.addEventListener('click', () => imageInput.click());
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('border-blue-400', 'bg-blue-400/10');
        });
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('border-blue-400', 'bg-blue-400/10');
        });
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-blue-400', 'bg-blue-400/10');
            const files = e.dataTransfer.files;
            if (files.length > 0) handleFileSelect(files[0]);
        });
        imageInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) handleFileSelect(e.target.files[0]);
        });
        removeFileBtn.addEventListener('click', removeFile);

        function handleFileSelect(file) {
            if (file.type.startsWith('image/')) {
                selectedFile = file;
                fileName.textContent = file.name;
                dropContent.classList.add('hidden');
                filePreview.classList.remove('hidden');
            }
        }

        function removeFile() {
            selectedFile = null;
            imageInput.value = '';
            dropContent.classList.remove('hidden');
            filePreview.classList.add('hidden');
        }

        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const text = textInput.value.trim();
            if (!text && !selectedFile) {
                alert('Please enter text or upload an image');
                return;
            }

            setLoading(true);

            try {
                const formData = new FormData();
                if (text) formData.append('text', text);
                if (selectedFile) formData.append('image', selectedFile);
                formData.append('language', languageSelect.value);

                // Try to call the API, fallback to demo mode if it fails
                try {
                    console.log('Attempting API call to:', `${API_BASE_URL}/api/analyze`);
                    console.log('Selected language:', languageSelect.value);

                    const response = await fetch(`${API_BASE_URL}/api/analyze`, {
                        method: 'POST',
                        body: formData
                    });

                    console.log('API Response status:', response.status);
                    const data = await response.json();
                    console.log('API Response data:', data);

                    if (data.success) {
                        console.log('✅ Using REAL API response in language:', data.data.language);
                        currentResult = data.data;
                        displayResults(data.data);
                    } else {
                        throw new Error(data.error || 'Analysis failed');
                    }
                } catch (apiError) {
                    console.log('❌ API not available, using demo mode:', apiError.message);
                    console.log('🎭 Demo mode - Selected language:', languageSelect.value);
                    // Demo mode - simulate analysis
                    const demoResult = createDemoAnalysis(text, selectedFile, languageSelect.value);
                    currentResult = demoResult;
                    displayResults(demoResult);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                setLoading(false);
            }
        });

        function setLoading(loading) {
            analyzeBtn.disabled = loading;
            if (loading) {
                btnText.classList.add('hidden');
                btnLoader.classList.remove('hidden');
            } else {
                btnText.classList.remove('hidden');
                btnLoader.classList.add('hidden');
            }
        }

        function createDemoAnalysis(text, hasImage, language) {
            // Enhanced demo analysis with better balance
            const fakeNewsKeywords = ['breaking', 'shocking', 'unbelievable', 'secret', 'exposed', 'leaked', 'doctors hate', 'miracle', 'darkness', 'alignment', 'urgent', 'exclusive', 'bombshell'];
            const realNewsKeywords = ['according to', 'study shows', 'research', 'published', 'scientists', 'official', 'confirmed', 'reported', 'announced', 'data shows', 'survey', 'analysis'];

            let fakeScore = 0;
            let realScore = 0;

            if (text) {
                const lowerText = text.toLowerCase();
                fakeScore = fakeNewsKeywords.filter(word => lowerText.includes(word)).length;
                realScore = realNewsKeywords.filter(word => lowerText.includes(word)).length;

                console.log(`Demo Analysis - Text: "${text.substring(0, 50)}..."`);
                console.log(`Fake indicators found: ${fakeScore} (${fakeNewsKeywords.filter(word => lowerText.includes(word))})`);
                console.log(`Real indicators found: ${realScore} (${realNewsKeywords.filter(word => lowerText.includes(word))})`);
            }

            // More balanced logic
            let isReal;
            if (fakeScore === 0 && realScore === 0) {
                // No clear indicators - make it more random but slightly favor real
                isReal = Math.random() > 0.4; // 60% chance of being real for neutral content
            } else if (fakeScore > realScore) {
                isReal = false; // Clear fake indicators
            } else if (realScore > fakeScore) {
                isReal = true; // Clear real indicators
            } else {
                // Equal scores - slight bias toward real for balanced results
                isReal = Math.random() > 0.45; // 55% chance of being real
            }

            // Calculate confidence based on the strength of indicators
            let baseConfidence = 75;
            const scoreDifference = Math.abs(fakeScore - realScore);
            const totalScore = fakeScore + realScore;

            if (totalScore > 0) {
                baseConfidence += (scoreDifference * 5); // More difference = higher confidence
                baseConfidence += (totalScore * 2); // More total indicators = higher confidence
            }

            const confidence = Math.min(95, Math.max(70, baseConfidence + (Math.random() * 10 - 5)));

            console.log(`Demo Result: ${isReal ? 'REAL' : 'FAKE'} with ${confidence.toFixed(1)}% confidence`);

            // Multilingual explanations
            const explanations = {
                'hi': {
                    real: 'यह सामग्री वैध समाचार रिपोर्टिंग की विशेषताओं को दर्शाती है। विश्वसनीय स्रोत और सत्यापन योग्य दावे मौजूद हैं।',
                    fake: 'विश्लेषण में गलत सूचना से जुड़े कई संकेतक मिले हैं, जिनमें सनसनीखेज भाषा और भावनात्मक हेरफेर शामिल है।',
                    recommendation_real: 'सामग्री विश्वसनीय लगती है, लेकिन साझा करने से पहले हमेशा कई विश्वसनीय स्रोतों से सत्यापन करें।',
                    recommendation_fake: 'इस सामग्री के साथ सावधानी बरतें। साझा करने से पहले कई विश्वसनीय समाचार स्रोतों से सत्यापन करें।'
                },
                'ta': {
                    real: 'இந்த உள்ளடக்கம் நம்பகமான செய்தி அறிக்கையின் பண்புகளைக் காட்டுகிறது। நம்பகமான ஆதாரங்கள் மற்றும் சரிபார்க்கக்கூடிய கூற்றுகள் உள்ளன.',
                    fake: 'பகுப்பாய்வில் தவறான தகவல்களுடன் தொடர்புடைய பல குறிகாட்டிகள் கண்டறியப்பட்டுள்ளன, இதில் பரபரப்பான மொழி மற்றும் உணர்ச்சிகரமான கையாளுதல் உள்ளது.',
                    recommendation_real: 'உள்ளடக்கம் நம்பகமானதாகத் தோன்றுகிறது, ஆனால் பகிர்வதற்கு முன் எப்போதும் பல நம்பகமான ஆதாரங்களிலிருந்து சரிபார்க்கவும்.',
                    recommendation_fake: 'இந்த உள்ளடக்கத்துடன் எச்சரிக்கையாக இருங்கள். பகிர்வதற்கு முன் பல நம்பகமான செய்தி ஆதாரங்களிலிருந்து சரிபார்க்கவும்.'
                },
                'te': {
                    real: 'ఈ కంటెంట్ చట్టబద్ధమైన వార్తా రిపోర్టింగ్ లక్షణాలను చూపిస్తుంది. విశ్వసనీయ మూలాలు మరియు ధృవీకరించదగిన వాదనలు ఉన్నాయి.',
                    fake: 'విశ్లేషణలో తప్పుడు సమాచారంతో సంబంధం ఉన్న అనేక సూచికలు కనుగొనబడ్డాయి, వీటిలో సంచలనాత్మక భాష మరియు భావోద్వేగ తారుమారు ఉన్నాయి.',
                    recommendation_real: 'కంటెంట్ విశ్వసనీయంగా కనిపిస్తుంది, కానీ భాగస్వామ్యం చేయడానికి ముందు ఎల్లప్పుడూ అనేక విశ్వసనీయ మూలాల నుండి ధృవీకరించండి.',
                    recommendation_fake: 'ఈ కంటెంట్‌తో జాగ్రత్తగా ఉండండి. భాగస్వామ్యం చేయడానికి ముందు అనేక విశ్వసనీయ వార్తా మూలాల ద్వారా ధృవీకరించండి.'
                },
                'en': {
                    real: 'This content shows characteristics of legitimate news reporting with factual language patterns. The analysis suggests credible source attribution and verifiable claims.',
                    fake: 'Analysis reveals several indicators commonly associated with misinformation, including sensationalized language and emotional manipulation tactics designed to encourage sharing without verification.',
                    recommendation_real: 'Content appears credible, but always verify through multiple reliable sources before sharing.',
                    recommendation_fake: 'Exercise caution with this content. Verify through multiple reliable news sources before sharing.'
                }
            };

            const langExplanations = explanations[language] || explanations['en'];

            return {
                isReal: isReal,
                confidence: Math.round(confidence),
                reasoning: isReal ? langExplanations.real : langExplanations.fake,
                sources: [
                    { name: isReal ? "Reuters" : "Unverified Source", credibility: isReal ? "High" : "Low" },
                    { name: isReal ? "Associated Press" : "Social Media", credibility: isReal ? "High" : "Low" }
                ],
                redFlags: isReal ? [] : ["Sensationalized language", "Emotional manipulation", "Unverified claims"],
                factualClaims: [text ? text.substring(0, 100) + "..." : "Image content analysis"],
                recommendation: isReal ? langExplanations.recommendation_real : langExplanations.recommendation_fake,
                debunkedBy: isReal ? [] : ["Demo Fact-Checker"],
                language: language,
                summary: `Analysis complete: ${isReal ? 'REAL' : 'FAKE'} (${Math.round(confidence)}% confidence)`,
                mlPrediction: {
                    isFake: !isReal,
                    confidence: confidence / 100,
                    model: "Demo ML Model"
                }
            };
        }

        function displayResults(result) {
            const statusColor = result.isReal ? 'text-green-400' : 'text-red-400';
            const statusBg = result.isReal ? 'bg-green-500/20 border-green-500/40' : 'bg-red-500/20 border-red-500/40';
            const statusIcon = result.isReal ? '✅' : '❌';
            const statusText = result.isReal ? 'Likely REAL' : 'Likely FAKE';

            let html = `
                <div class="text-center mb-6 md:mb-8">
                    <div class="text-4xl md:text-6xl mb-3 md:mb-4">${statusIcon}</div>
                    <h3 class="text-2xl md:text-4xl font-bold ${statusColor} mb-3 md:mb-4">${statusText}</h3>
                    <div class="inline-block px-4 md:px-6 py-2 ${statusBg} border rounded-full text-lg md:text-xl font-semibold">
                        Confidence: ${result.confidence}%
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6">
                    <div class="bg-black/40 p-4 md:p-6 rounded-xl border border-orange-400/30">
                        <h4 class="text-lg md:text-xl font-semibold mb-3 flex items-center gap-2 text-orange-300">
                            🤖 ML Model Prediction
                        </h4>
                        ${result.mlPrediction ? `
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-white/80">Model:</span>
                                    <span class="text-cyan-400 font-semibold">${result.mlPrediction.model}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-white/80">Prediction:</span>
                                    <span class="px-2 py-1 rounded-full text-xs font-semibold ${
                                        result.mlPrediction.isFake ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'
                                    }">${result.mlPrediction.isFake ? 'FAKE' : 'REAL'}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-white/80">ML Confidence:</span>
                                    <span class="text-orange-400 font-semibold">${(result.mlPrediction.confidence * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                        ` : ''}
                        <h5 class="text-md font-semibold mb-2 text-orange-300">🧠 AI Explanation</h5>
                        <p class="text-white/90 leading-relaxed text-sm md:text-base">${result.reasoning}</p>
                    </div>

                    <div class="bg-black/40 p-4 md:p-6 rounded-xl border border-cyan-400/30">
                        <h4 class="text-lg md:text-xl font-semibold mb-3 flex items-center gap-2 text-cyan-300">
                            📊 Analysis Details
                        </h4>
                        <div class="space-y-2">
                            ${result.sources.map(source => `
                                <div class="flex flex-col md:flex-row md:justify-between md:items-center p-2 bg-white/10 rounded-lg gap-1 md:gap-0">
                                    <span class="text-sm md:text-base">${source.name}</span>
                                    <span class="px-2 py-1 text-xs rounded-full w-fit ${
                                        source.credibility === 'High' ? 'bg-green-500/20 text-green-400' :
                                        source.credibility === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' :
                                        'bg-red-500/20 text-red-400'
                                    }">${source.credibility}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            if (result.redFlags && result.redFlags.length > 0) {
                html += `
                    <div class="bg-red-500/10 border border-red-500/30 p-4 md:p-6 rounded-xl mb-4 md:mb-6">
                        <h4 class="text-lg md:text-xl font-semibold mb-3 text-red-400 flex items-center gap-2">
                            🚩 Warning Signs
                        </h4>
                        <div class="flex flex-wrap gap-2">
                            ${result.redFlags.map(flag => `
                                <span class="px-2 md:px-3 py-1 bg-red-500/20 border border-red-500/40 rounded-full text-xs md:text-sm">${flag}</span>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            if (result.debunkedBy && result.debunkedBy.length > 0) {
                html += `
                    <div class="bg-purple-500/10 border border-purple-500/30 p-4 md:p-6 rounded-xl mb-4 md:mb-6">
                        <h4 class="text-lg md:text-xl font-semibold mb-3 text-purple-400 flex items-center gap-2">
                            ❌ Previously Debunked By
                        </h4>
                        <div class="flex flex-wrap gap-2">
                            ${result.debunkedBy.map(org => `
                                <span class="px-2 md:px-3 py-1 bg-purple-500/20 border border-purple-500/40 rounded-full text-xs md:text-sm font-semibold">${org}</span>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            html += `
                <div class="bg-yellow-500/10 border border-yellow-500/30 p-4 md:p-6 rounded-xl text-center">
                    <h4 class="text-lg md:text-xl font-semibold mb-3 text-yellow-400 flex items-center justify-center gap-2">
                        💡 Recommendation
                    </h4>
                    <p class="text-white/90 text-base md:text-lg leading-relaxed">${result.recommendation}</p>
                </div>
            `;

            resultsContent.innerHTML = html;
            resultsSection.classList.remove('hidden');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // Action buttons
        analyzeAnotherBtn.addEventListener('click', () => {
            textInput.value = '';
            removeFile();
            resultsSection.classList.add('hidden');
            currentResult = null;
        });

        copyResultsBtn.addEventListener('click', () => {
            if (currentResult) {
                const text = `TruthLens AI Analysis: ${currentResult.isReal ? 'REAL' : 'FAKE'} (${currentResult.confidence}% confidence) - ${currentResult.reasoning}`;
                navigator.clipboard.writeText(text).then(() => {
                    copyResultsBtn.innerHTML = '✅ Copied!';
                    setTimeout(() => {
                        copyResultsBtn.innerHTML = '📋 Copy Results';
                    }, 2000);
                });
            }
        });

        speakResultsBtn.addEventListener('click', async () => {
            if (currentResult) {
                const originalText = speakResultsBtn.innerHTML;
                speakResultsBtn.innerHTML = '🔄 Generating Audio...';
                speakResultsBtn.disabled = true;

                try {
                    const textToSpeak = currentResult.summary || currentResult.reasoning || currentResult.recommendation;
                    const selectedLanguage = currentResult.language || languageSelect.value;

                    console.log('TTS Request:', { text: textToSpeak.substring(0, 50), language: selectedLanguage });

                    const response = await fetch(`${API_BASE_URL}/api/text-to-speech`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            text: textToSpeak,
                            language: selectedLanguage
                        })
                    });

                    console.log('TTS Response status:', response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log('TTS Response data:', data.success ? 'Success' : data.error);

                    if (data.success && data.audio) {
                        // Create audio element and play
                        const audio = new Audio(`data:audio/mp3;base64,${data.audio}`);

                        speakResultsBtn.innerHTML = '🔊 Playing...';

                        audio.onended = () => {
                            speakResultsBtn.innerHTML = originalText;
                            speakResultsBtn.disabled = false;
                        };

                        audio.onerror = (e) => {
                            console.error('Audio playback error:', e);
                            speakResultsBtn.innerHTML = originalText;
                            speakResultsBtn.disabled = false;
                            alert('Audio playback failed. Please try again.');
                        };

                        // Play the audio
                        try {
                            await audio.play();
                        } catch (playError) {
                            console.error('Play error:', playError);
                            throw new Error('Could not play audio');
                        }
                    } else {
                        throw new Error(data.error || 'Text-to-speech generation failed');
                    }
                } catch (error) {
                    console.error('TTS Error:', error);
                    alert(`Text-to-speech error: ${error.message}`);
                    speakResultsBtn.innerHTML = originalText;
                    speakResultsBtn.disabled = false;
                }
            }
        });
    </script>
</body>
</html>
