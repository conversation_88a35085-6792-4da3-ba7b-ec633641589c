#!/bin/bash

# TruthLens AI - Free Tier Optimized Deployment Script
# Optimized for Google Cloud Platform Free Tier usage

set -e

echo "🚀 TruthLens AI - Free Tier Optimized Deployment"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Free tier optimized configuration
REGION="us-central1"  # Best region for free tier
MEMORY="1Gi"          # Free tier memory limit
CPU="1"               # Free tier CPU limit
MAX_INSTANCES="3"     # Cost control
MIN_INSTANCES="0"     # Scale to zero for cost savings
TIMEOUT="300"         # Request timeout
CONCURRENCY="80"      # Optimize concurrency

echo -e "${PURPLE}💰 Free Tier Configuration:${NC}"
echo "  Region: $REGION (Free tier optimized)"
echo "  Memory: $MEMORY (Within free limits)"
echo "  CPU: $CPU core (Within free limits)"
echo "  Max Instances: $MAX_INSTANCES (Cost control)"
echo "  Min Instances: $MIN_INSTANCES (Scale to zero)"
echo "  Concurrency: $CONCURRENCY (Optimized)"
echo ""

# Function to handle errors
handle_error() {
    echo -e "${RED}❌ Error: $1${NC}"
    exit 1
}

# Function to run command with error handling
run_command() {
    echo -e "${BLUE}🔧 $1${NC}"
    if eval "$1"; then
        echo -e "${GREEN}✅ Success${NC}"
    else
        handle_error "Command failed: $1"
    fi
}

# Check prerequisites
echo -e "${YELLOW}🔧 Checking prerequisites...${NC}"

if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI is required${NC}"
    echo "Install: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

if ! command -v firebase &> /dev/null; then
    echo -e "${RED}❌ firebase CLI is required${NC}"
    echo "Install: npm install -g firebase-tools"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites met${NC}"

# Get project ID
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}❌ No Google Cloud project set${NC}"
    echo "Run: gcloud config set project YOUR_PROJECT_ID"
    exit 1
fi

echo -e "${BLUE}📋 Project: ${PROJECT_ID}${NC}"

# Check authentication
echo -e "${YELLOW}🔐 Checking authentication...${NC}"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${YELLOW}🔑 Please authenticate...${NC}"
    gcloud auth login
fi

# Set region
run_command "gcloud config set run/region $REGION"

# Enable APIs
echo -e "${YELLOW}🔌 Enabling APIs (Free tier: 120 build-minutes/day)...${NC}"
run_command "gcloud services enable cloudbuild.googleapis.com --quiet"
run_command "gcloud services enable run.googleapis.com --quiet"
run_command "gcloud services enable containerregistry.googleapis.com --quiet"

# Check quotas
echo -e "${PURPLE}📊 Free Tier Quotas:${NC}"
echo "  ✅ Cloud Run: 2M requests/month"
echo "  ✅ Cloud Build: 120 build-minutes/day"
echo "  ✅ Container Registry: 0.5GB storage"
echo "  ✅ Firebase Hosting: 10GB transfer/month"
echo ""

# Deploy backend with free tier optimizations
echo -e "${YELLOW}🏗️  Deploying backend (Free tier optimized)...${NC}"
echo "  Using optimized build configuration..."
echo "  This may take 3-5 minutes..."

# Build with timeout and optimizations
run_command "gcloud builds submit --config cloudbuild.yaml --timeout=1200s"

# Get backend URL
echo -e "${YELLOW}🔗 Getting backend URL...${NC}"
BACKEND_URL=""
for i in {1..5}; do
    BACKEND_URL=$(gcloud run services describe truthlens-backend --region=$REGION --format="value(status.url)" 2>/dev/null)
    if [ ! -z "$BACKEND_URL" ]; then
        break
    fi
    echo "  Waiting for service... (attempt $i/5)"
    sleep 10
done

if [ -z "$BACKEND_URL" ]; then
    handle_error "Failed to get backend URL"
fi

echo -e "${GREEN}✅ Backend: ${BACKEND_URL}${NC}"

# Verify backend health
echo -e "${YELLOW}🏥 Health check...${NC}"
if curl -s "$BACKEND_URL/api/health" | grep -q "OK"; then
    echo -e "${GREEN}✅ Backend healthy${NC}"
else
    echo -e "${YELLOW}⚠️  Backend warming up...${NC}"
fi

# Update frontend
echo -e "${YELLOW}🔄 Updating frontend...${NC}"
cp public/index.html public/index.html.backup 2>/dev/null || true

if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s|https://truthlens-backend-.*-uc\.a\.run\.app|$BACKEND_URL|g" public/index.html
    sed -i '' "s|https://truthlens-backend-.*-uc\.a\.run\.app|$BACKEND_URL|g" templates/index.html
else
    sed -i "s|https://truthlens-backend-.*-uc\.a\.run\.app|$BACKEND_URL|g" public/index.html
    sed -i "s|https://truthlens-backend-.*-uc\.a\.run\.app|$BACKEND_URL|g" templates/index.html
fi

# Deploy frontend
echo -e "${YELLOW}🌐 Deploying frontend (Free: 10GB/month)...${NC}"
run_command "firebase use $PROJECT_ID --quiet"
run_command "firebase deploy --only hosting --quiet"

FRONTEND_URL="https://$PROJECT_ID.web.app"
FRONTEND_ALT="https://$PROJECT_ID.firebaseapp.com"

# Final verification
echo -e "${YELLOW}🧪 Final verification...${NC}"
if curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" | grep -q "200"; then
    echo -e "${GREEN}✅ Frontend accessible${NC}"
else
    echo -e "${YELLOW}⚠️  Frontend may need time to propagate${NC}"
fi

echo ""
echo -e "${GREEN}🎉 FREE TIER DEPLOYMENT COMPLETE!${NC}"
echo "================================================"
echo ""
echo -e "${BLUE}🌐 Your AI-Powered Fake News Detection System:${NC}"
echo -e "   Primary:   ${GREEN}$FRONTEND_URL${NC}"
echo -e "   Alternate: ${GREEN}$FRONTEND_ALT${NC}"
echo -e "   Backend:   ${GREEN}$BACKEND_URL${NC}"
echo ""
echo -e "${PURPLE}💰 Free Tier Optimization Summary:${NC}"
echo "   ✅ Memory: $MEMORY (within limits)"
echo "   ✅ CPU: $CPU core (within limits)"
echo "   ✅ Scale to zero when idle"
echo "   ✅ Optimized for 2M requests/month"
echo "   ✅ Cost: $0-5/month for moderate usage"
echo ""
echo -e "${YELLOW}🧪 Test Your System:${NC}"
echo "   1. Visit: $FRONTEND_URL"
echo "   2. Test fake: 'SHOCKING: Doctors hate this trick!'"
echo "   3. Test real: 'Weather forecast shows rain tomorrow'"
echo "   4. Upload image for OCR analysis"
echo "   5. Try different languages (23 supported)"
echo "   6. Test text-to-speech feature"
echo ""
echo -e "${BLUE}📊 Monitor Usage:${NC}"
echo "   • Cloud Console: https://console.cloud.google.com/run"
echo "   • Firebase: https://console.firebase.google.com"
echo "   • Billing: https://console.cloud.google.com/billing"
echo ""
echo -e "${GREEN}🎯 Features Deployed:${NC}"
echo "   ✅ ML-powered fake news detection"
echo "   ✅ Advanced image analysis & OCR"
echo "   ✅ Gemini AI explanations"
echo "   ✅ 23 language support"
echo "   ✅ Text-to-speech"
echo "   ✅ Mobile-responsive design"
echo ""
echo -e "${GREEN}✨ Your free-tier optimized system is live!${NC}"
